<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jamendo Integration Test - Banshee Music</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #00ff88;
        }
        .test-button {
            background: #00ff88;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        .test-button:hover {
            background: #00cc6a;
        }
        .test-button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .results {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
        }
        .track-item {
            background: #444;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .track-item img {
            width: 50px;
            height: 50px;
            border-radius: 5px;
        }
        .track-info {
            flex: 1;
        }
        .track-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .track-artist {
            color: #ccc;
            font-size: 0.9em;
        }
        .play-button {
            background: #00ff88;
            color: #000;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8em;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success { background: #2d5a2d; }
        .status.error { background: #5a2d2d; }
        .status.warning { background: #5a5a2d; }
        .audio-player {
            margin: 20px 0;
            text-align: center;
        }
        audio {
            width: 100%;
            max-width: 500px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎵 Jamendo Integration Test</h1>
        <p>Test the Jamendo API integration for Banshee Music</p>
    </div>

    <div class="test-section">
        <h2>Service Health Check</h2>
        <button class="test-button" onclick="testHealthCheck()">Test API Health</button>
        <div id="health-results" class="results"></div>
    </div>

    <div class="test-section">
        <h2>Featured Tracks</h2>
        <button class="test-button" onclick="testFeaturedTracks()">Load Featured Tracks</button>
        <div id="featured-results" class="results"></div>
    </div>

    <div class="test-section">
        <h2>Search Music</h2>
        <input type="text" id="search-input" placeholder="Search for music..." style="padding: 8px; margin-right: 10px; border-radius: 5px; border: none; width: 200px;">
        <button class="test-button" onclick="testSearch()">Search</button>
        <div id="search-results" class="results"></div>
    </div>

    <div class="test-section">
        <h2>Direct API Test</h2>
        <button class="test-button" onclick="testDirectAPI()">Test Direct Deezer Call</button>
        <div id="direct-results" class="results"></div>
    </div>

    <div class="test-section">
        <h2>Hybrid Service Test</h2>
        <button class="test-button" onclick="testHybridService()">Test Hybrid Search</button>
        <div id="hybrid-results" class="results"></div>
    </div>

    <div class="audio-player">
        <h3>Audio Player</h3>
        <audio id="test-audio" controls>
            Your browser does not support the audio element.
        </audio>
        <div id="now-playing"></div>
    </div>

    <!-- Include Banshee services -->
    <script src="js/utils.js"></script>
    <script src="js/services/deezer-service.js"></script>
    <script src="js/services/jamendo-service.js"></script>
    <script src="js/services/hybrid-music-service.js"></script>

    <script>
        let currentAudio = document.getElementById('test-audio');
        let nowPlaying = document.getElementById('now-playing');

        // Initialize services
        window.addEventListener('DOMContentLoaded', async () => {
            showStatus('Initializing services...', 'warning');
            
            try {
                if (window.hybridMusicService) {
                    await window.hybridMusicService.init();
                    showStatus('✅ Services initialized successfully!', 'success');
                } else {
                    showStatus('❌ Hybrid music service not available', 'error');
                }
            } catch (error) {
                showStatus(`❌ Initialization failed: ${error.message}`, 'error');
            }
        });

        function showStatus(message, type = 'success') {
            const status = document.createElement('div');
            status.className = `status ${type}`;
            status.textContent = message;
            document.body.insertBefore(status, document.body.firstChild);
            
            setTimeout(() => {
                if (status.parentNode) {
                    status.parentNode.removeChild(status);
                }
            }, 5000);
        }

        async function testHealthCheck() {
            const resultsDiv = document.getElementById('health-results');
            resultsDiv.innerHTML = '<p>Testing API health...</p>';

            try {
                // Test if services exist first
                if (!window.jamendoService) {
                    throw new Error('Jamendo service not loaded');
                }
                if (!window.deezerService) {
                    throw new Error('Deezer service not loaded');
                }

                let jamendoHealth = false;
                let deezerHealth = false;
                let jamendoError = '';
                let deezerError = '';

                // Test Jamendo health
                try {
                    jamendoHealth = await window.jamendoService.healthCheck();
                } catch (error) {
                    jamendoError = error.message;
                    console.error('Jamendo health check error:', error);
                }

                // Test Deezer health
                try {
                    deezerHealth = await window.deezerService.healthCheck();
                } catch (error) {
                    deezerError = error.message;
                    console.error('Deezer health check error:', error);
                }

                // Test basic connectivity
                let networkTest = '';
                try {
                    const response = await fetch('https://httpbin.org/get');
                    networkTest = response.ok ? '✅ Network connectivity OK' : '❌ Network issues';
                } catch (error) {
                    networkTest = '❌ Network connectivity failed';
                }

                resultsDiv.innerHTML = `
                    <h4>Service Status:</h4>
                    <p>🎵 Jamendo API: ${jamendoHealth ? '✅ Healthy' : '❌ Unhealthy'}</p>
                    ${jamendoError ? `<p style="color: #ff6b6b; font-size: 0.9em;">   Error: ${jamendoError}</p>` : ''}
                    <p>🎵 Deezer API: ${deezerHealth ? '✅ Healthy' : '❌ Unhealthy'}</p>
                    ${deezerError ? `<p style="color: #ff6b6b; font-size: 0.9em;">   Error: ${deezerError}</p>` : ''}
                    <p>🔧 Jamendo Configured: ${window.jamendoService.isConfigured() ? '✅ Yes' : '❌ No - Check JAMENDO_SETUP.md'}</p>
                    <p>🔧 Services Loaded: ✅ All services loaded successfully</p>
                    <p>🌐 ${networkTest}</p>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `<p>❌ Health check failed: ${error.message}</p>`;
                console.error('Health check error:', error);
            }
        }

        async function testFeaturedTracks() {
            const resultsDiv = document.getElementById('featured-results');
            resultsDiv.innerHTML = '<p>Loading featured tracks...</p>';

            try {
                const featured = await window.jamendoService.getFeaturedTracks(5);
                
                if (featured.tracks && featured.tracks.length > 0) {
                    resultsDiv.innerHTML = '<h4>Featured Tracks:</h4>' + 
                        featured.tracks.map(track => createTrackHTML(track)).join('');
                } else {
                    resultsDiv.innerHTML = '<p>No featured tracks found</p>';
                }
            } catch (error) {
                resultsDiv.innerHTML = `<p>❌ Failed to load featured tracks: ${error.message}</p>`;
            }
        }

        async function testSearch() {
            const query = document.getElementById('search-input').value;
            const resultsDiv = document.getElementById('search-results');
            
            if (!query) {
                resultsDiv.innerHTML = '<p>Please enter a search term</p>';
                return;
            }

            resultsDiv.innerHTML = '<p>Searching...</p>';

            try {
                const results = await window.jamendoService.search(query, 'track', 5);
                
                if (results.data && results.data.length > 0) {
                    resultsDiv.innerHTML = `<h4>Search Results for "${query}":</h4>` + 
                        results.data.map(track => createTrackHTML(track)).join('');
                } else {
                    resultsDiv.innerHTML = `<p>No results found for "${query}"</p>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<p>❌ Search failed: ${error.message}</p>`;
            }
        }

        async function testDirectAPI() {
            const resultsDiv = document.getElementById('direct-results');
            resultsDiv.innerHTML = '<p>Testing direct API calls...</p>';

            try {
                // Test direct fetch to Deezer API
                console.log('Testing direct Deezer API call...');
                const deezerUrl = 'https://corsproxy.io/?https://api.deezer.com/chart';
                const response = await fetch(deezerUrl);

                if (response.ok) {
                    const data = await response.json();
                    resultsDiv.innerHTML = `
                        <h4>Direct API Test Results:</h4>
                        <p>✅ Deezer API Response: ${response.status} ${response.statusText}</p>
                        <p>📊 Tracks found: ${data.tracks?.data?.length || 0}</p>
                        <p>🎵 First track: ${data.tracks?.data?.[0]?.title || 'None'}</p>
                        <pre style="background: #444; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 0.8em;">
${JSON.stringify(data.tracks?.data?.[0] || {}, null, 2).substring(0, 500)}...
                        </pre>
                    `;
                } else {
                    resultsDiv.innerHTML = `<p>❌ Direct API call failed: ${response.status} ${response.statusText}</p>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<p>❌ Direct API test failed: ${error.message}</p>`;
                console.error('Direct API error:', error);
            }
        }

        async function testHybridService() {
            const resultsDiv = document.getElementById('hybrid-results');
            resultsDiv.innerHTML = '<p>Testing hybrid service...</p>';

            try {
                const results = await window.hybridMusicService.search('rock', 'track', 5);

                if (results.data && results.data.length > 0) {
                    resultsDiv.innerHTML = `<h4>Hybrid Search Results:</h4>` +
                        results.data.map(track => createTrackHTML(track)).join('');
                } else {
                    resultsDiv.innerHTML = '<p>No hybrid results found</p>';
                }
            } catch (error) {
                resultsDiv.innerHTML = `<p>❌ Hybrid search failed: ${error.message}</p>`;
            }
        }

        function createTrackHTML(track) {
            const playable = track.url && track.playable !== false;
            return `
                <div class="track-item">
                    <img src="${track.cover || 'imgs/album-01.png'}" alt="${track.title}" onerror="this.src='imgs/album-01.png'">
                    <div class="track-info">
                        <div class="track-title">${track.title}</div>
                        <div class="track-artist">${track.artist} ${track.source ? `(${track.source})` : ''}</div>
                        ${track.genre ? `<div style="font-size: 0.8em; color: #999;">Genre: ${track.genre}</div>` : ''}
                    </div>
                    ${playable ? 
                        `<button class="play-button" onclick="playTrack('${track.url}', '${track.title}', '${track.artist}')">▶ Play</button>` :
                        '<span style="color: #999; font-size: 0.8em;">Preview Only</span>'
                    }
                </div>
            `;
        }

        function playTrack(url, title, artist) {
            if (url && url !== 'undefined') {
                currentAudio.src = url;
                currentAudio.play();
                nowPlaying.innerHTML = `<strong>Now Playing:</strong> ${title} by ${artist}`;
                showStatus(`🎵 Playing: ${title} by ${artist}`, 'success');
            } else {
                showStatus('❌ No audio URL available for this track', 'error');
            }
        }
    </script>
</body>
</html>
