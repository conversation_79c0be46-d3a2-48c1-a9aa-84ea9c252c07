// Hybrid Music Service - Combines Jamendo (playable) + <PERSON><PERSON> (metadata)
// Provides the best of both worlds: real playable music + rich metadata
class HybridMusicService {
    constructor() {
        this.jamendoService = window.jamendoService;
        this.deezerService = window.deezerService;
        this.preferJamendo = true; // Prefer Jamendo for actual playback
        this.cache = new Map();
        this.cacheTimeout = 10 * 60 * 1000; // 10 minutes
    }

    // Initialize services and check health
    async init() {
        console.log('🎵 Initializing Hybrid Music Service...');
        
        const jamendoHealthy = await this.jamendoService.healthCheck();
        const deezerHealthy = await this.deezerService.healthCheck();
        
        console.log(`🎵 Service Status - Jamendo: ${jamendoHealthy ? '✅' : '❌'}, Deezer: ${deezerHealthy ? '✅' : '❌'}`);
        
        // Adjust strategy based on service availability
        if (!jamendoHealthy && deezerHealthy) {
            console.log('⚠️ Jamendo unavailable, falling back to <PERSON><PERSON> only');
            this.preferJamendo = false;
        } else if (jamendoHealthy && !deezerHealthy) {
            console.log('⚠️ <PERSON><PERSON> unavailable, using Jamendo only');
        }
        
        return { jamendo<PERSON>ealthy, deezer<PERSON>ealthy };
    }

    // Smart search that combines both services
    async search(query, type = 'track', limit = 25) {
        if (!query || query.trim().length < 2) {
            return { data: [], total: 0, sources: [] };
        }

        const cacheKey = `hybrid_search_${type}_${query}_${limit}`;
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                console.log(`🎵 Using cached hybrid search: ${query}`);
                return cached.data;
            }
        }

        try {
            console.log(`🎵 Hybrid search for: "${query}" (${type})`);
            
            // Search both services in parallel
            const [jamendoResults, deezerResults] = await Promise.allSettled([
                this.jamendoService.search(query, type, Math.ceil(limit * 0.7)), // 70% from Jamendo
                this.deezerService.search(query, type, Math.ceil(limit * 0.3))   // 30% from Deezer
            ]);

            let combinedData = [];
            let sources = [];

            // Process Jamendo results (preferred for playability)
            if (jamendoResults.status === 'fulfilled' && jamendoResults.value.data) {
                const jamendoTracks = jamendoResults.value.data.map(track => ({
                    ...track,
                    playable: true,
                    source: 'jamendo',
                    priority: 1 // Higher priority for playable content
                }));
                combinedData.push(...jamendoTracks);
                sources.push('jamendo');
            }

            // Process Deezer results (for metadata richness)
            if (deezerResults.status === 'fulfilled' && deezerResults.value.data) {
                const deezerTracks = deezerResults.value.data.map(track => ({
                    ...track,
                    playable: false,
                    source: 'deezer',
                    priority: 2 // Lower priority (metadata only)
                }));
                combinedData.push(...deezerTracks);
                sources.push('deezer');
            }

            // Sort by priority (Jamendo first) and relevance
            combinedData.sort((a, b) => {
                if (a.priority !== b.priority) return a.priority - b.priority;
                // Additional sorting logic could be added here
                return 0;
            });

            // Limit results
            combinedData = combinedData.slice(0, limit);

            const result = {
                data: combinedData,
                total: combinedData.length,
                sources: [...new Set(sources)]
            };

            // Cache the result
            this.cache.set(cacheKey, {
                data: result,
                timestamp: Date.now()
            });

            console.log(`✅ Hybrid search completed: ${combinedData.length} results from ${sources.join(', ')}`);
            return result;

        } catch (error) {
            console.error('❌ Hybrid search failed:', error);
            return { data: [], total: 0, sources: [] };
        }
    }

    // Get featured/trending content with hybrid approach
    async getFeaturedContent(limit = 25, genre = null) {
        const cacheKey = `hybrid_featured_${genre || 'all'}_${limit}`;
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }

        try {
            console.log(`🎵 Getting hybrid featured content${genre ? ` for ${genre}` : ''}`);

            // Get featured content from both services
            const [jamendoFeatured, deezerCharts] = await Promise.allSettled([
                this.jamendoService.getFeaturedTracks(Math.ceil(limit * 0.8), genre),
                this.deezerService.getCharts()
            ]);

            let combinedTracks = [];

            // Add Jamendo featured tracks (playable)
            if (jamendoFeatured.status === 'fulfilled' && jamendoFeatured.value.tracks) {
                const tracks = jamendoFeatured.value.tracks.map(track => ({
                    ...track,
                    playable: true,
                    featured: true,
                    source: 'jamendo'
                }));
                combinedTracks.push(...tracks);
            }

            // Add some Deezer chart tracks for variety (metadata only)
            if (deezerCharts.status === 'fulfilled' && deezerCharts.value?.tracks) {
                const deezerTracks = deezerCharts.value.tracks
                    .slice(0, Math.ceil(limit * 0.2))
                    .map(track => ({
                        ...track,
                        playable: false,
                        featured: true,
                        source: 'deezer'
                    }));
                combinedTracks.push(...deezerTracks);
            }

            // Shuffle and limit
            combinedTracks = this.shuffleArray(combinedTracks).slice(0, limit);

            const result = {
                tracks: combinedTracks,
                total: combinedTracks.length
            };

            this.cache.set(cacheKey, {
                data: result,
                timestamp: Date.now()
            });

            return result;

        } catch (error) {
            console.error('❌ Failed to get hybrid featured content:', error);
            return { tracks: [], total: 0 };
        }
    }

    // Get tracks by genre with hybrid approach
    async getTracksByGenre(genre, limit = 25) {
        try {
            console.log(`🎵 Getting hybrid tracks for genre: ${genre}`);

            // Prioritize Jamendo for playable content
            const jamendoTracks = await this.jamendoService.getTracksByGenre(genre, limit);
            
            if (jamendoTracks.tracks && jamendoTracks.tracks.length > 0) {
                return {
                    tracks: jamendoTracks.tracks.map(track => ({
                        ...track,
                        playable: true,
                        source: 'jamendo'
                    })),
                    total: jamendoTracks.total
                };
            }

            // Fallback to Deezer if Jamendo has no results
            console.log('⚠️ No Jamendo tracks found, falling back to Deezer');
            const deezerResults = await this.deezerService.search(genre, 'track', limit);
            
            return {
                tracks: deezerResults.data.map(track => ({
                    ...track,
                    playable: false,
                    source: 'deezer'
                })),
                total: deezerResults.total
            };

        } catch (error) {
            console.error(`❌ Failed to get tracks for genre ${genre}:`, error);
            return { tracks: [], total: 0 };
        }
    }

    // Get playlists (Jamendo only for now)
    async getPlaylists(limit = 20) {
        try {
            const playlists = await this.jamendoService.getPlaylists(limit);
            return {
                playlists: playlists.playlists.map(playlist => ({
                    ...playlist,
                    playable: true,
                    source: 'jamendo'
                })),
                total: playlists.total
            };
        } catch (error) {
            console.error('❌ Failed to get hybrid playlists:', error);
            return { playlists: [], total: 0 };
        }
    }

    // Get a single track with enhanced metadata
    async getTrack(trackId, source = 'jamendo') {
        try {
            if (source === 'jamendo') {
                // Get track from Jamendo (playable)
                const tracks = await this.jamendoService.search(`id:${trackId}`, 'track', 1);
                if (tracks.data && tracks.data.length > 0) {
                    return {
                        ...tracks.data[0],
                        playable: true,
                        source: 'jamendo'
                    };
                }
            } else if (source === 'deezer') {
                // Get track from Deezer (metadata only)
                const track = await this.deezerService.getTrack(trackId);
                if (track) {
                    return {
                        ...track,
                        playable: false,
                        source: 'deezer'
                    };
                }
            }
            return null;
        } catch (error) {
            console.error(`❌ Failed to get track ${trackId}:`, error);
            return null;
        }
    }

    // Utility: Shuffle array
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    // Get service status
    async getStatus() {
        return {
            jamendoConfigured: this.jamendoService.isConfigured(),
            jamendoHealthy: await this.jamendoService.healthCheck(),
            deezerHealthy: await this.deezerService.healthCheck(),
            preferJamendo: this.preferJamendo
        };
    }

    // Configure Jamendo client ID
    configureJamendo(clientId) {
        this.jamendoService.setClientId(clientId);
        console.log('🎵 Jamendo configured in hybrid service');
    }

    // Clear cache
    clearCache() {
        this.cache.clear();
        console.log('🎵 Hybrid service cache cleared');
    }
}

// Create global instance
window.hybridMusicService = new HybridMusicService();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HybridMusicService;
}
