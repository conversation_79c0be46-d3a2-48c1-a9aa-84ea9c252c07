// Shared Deezer API Service for Banshee Music App
class DeezerService {
    constructor() {
        this.baseUrl = 'https://corsproxy.io/?https://api.deezer.com';
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
        this.rateLimitDelay = 100; // 100ms between requests
        this.lastRequestTime = 0;
    }

    // Rate limiting helper
    async waitForRateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        if (timeSinceLastRequest < this.rateLimitDelay) {
            await new Promise(resolve => setTimeout(resolve, this.rateLimitDelay - timeSinceLastRequest));
        }
        this.lastRequestTime = Date.now();
    }

    // Generic API request with caching and error handling
    async makeRequest(endpoint, cacheKey = null) {
        // Check cache first
        if (cacheKey && this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                console.log(`🎵 Using cached data for: ${cacheKey}`);
                return cached.data;
            }
        }

        await this.waitForRateLimit();

        try {
            console.log(`🎵 Fetching from Deezer: ${endpoint}`);
            const response = await fetch(`${this.baseUrl}/${endpoint}`);
            
            if (!response.ok) {
                throw new Error(`Deezer API error: ${response.status}`);
            }

            const data = await response.json();

            // Cache the result
            if (cacheKey) {
                this.cache.set(cacheKey, {
                    data,
                    timestamp: Date.now()
                });
            }

            return data;
        } catch (error) {
            console.error(`❌ Deezer API request failed for ${endpoint}:`, error);
            throw error;
        }
    }

    // Get trending charts
    async getCharts() {
        try {
            const data = await this.makeRequest('chart', 'charts');
            return this.formatChartsData(data);
        } catch (error) {
            console.error('Failed to fetch Deezer charts:', error);
            return null;
        }
    }

    // Search for tracks, artists, albums
    async search(query, type = 'track', limit = 25) {
        if (!query || query.trim().length < 2) {
            return { data: [], total: 0 };
        }

        const endpoint = type === 'all' 
            ? `search?q=${encodeURIComponent(query)}&limit=${limit}`
            : `search/${type}?q=${encodeURIComponent(query)}&limit=${limit}`;
        
        const cacheKey = `search_${type}_${query}_${limit}`;

        try {
            const data = await this.makeRequest(endpoint, cacheKey);
            return this.formatSearchData(data, type);
        } catch (error) {
            console.error(`Failed to search Deezer for "${query}":`, error);
            return { data: [], total: 0 };
        }
    }

    // Get artist details
    async getArtist(artistId) {
        try {
            const [artist, topTracks, albums] = await Promise.all([
                this.makeRequest(`artist/${artistId}`, `artist_${artistId}`),
                this.makeRequest(`artist/${artistId}/top?limit=10`, `artist_top_${artistId}`),
                this.makeRequest(`artist/${artistId}/albums?limit=10`, `artist_albums_${artistId}`)
            ]);

            return this.formatArtistData(artist, topTracks, albums);
        } catch (error) {
            console.error(`Failed to fetch artist ${artistId}:`, error);
            return null;
        }
    }

    // Get album details
    async getAlbum(albumId) {
        try {
            const album = await this.makeRequest(`album/${albumId}`, `album_${albumId}`);
            return this.formatAlbumData(album);
        } catch (error) {
            console.error(`Failed to fetch album ${albumId}:`, error);
            return null;
        }
    }

    // Get playlist details
    async getPlaylist(playlistId) {
        try {
            const playlist = await this.makeRequest(`playlist/${playlistId}`, `playlist_${playlistId}`);
            return this.formatPlaylistData(playlist);
        } catch (error) {
            console.error(`Failed to fetch playlist ${playlistId}:`, error);
            return null;
        }
    }

    // Get genre-based recommendations
    async getGenreRecommendations(genreId, limit = 20) {
        try {
            const data = await this.makeRequest(`genre/${genreId}/artists?limit=${limit}`, `genre_${genreId}_${limit}`);
            return {
                genre: genreId,
                artists: data.data?.map(artist => this.formatArtist(artist)) || [],
                total: data.total || 0
            };
        } catch (error) {
            console.error(`Failed to fetch genre recommendations for ${genreId}:`, error);
            return { genre: genreId, artists: [], total: 0 };
        }
    }

    // Get radio stations
    async getRadioStations(limit = 10) {
        try {
            const data = await this.makeRequest(`radio?limit=${limit}`, `radio_${limit}`);
            return {
                stations: data.data?.map(station => this.formatRadioStation(station)) || [],
                total: data.total || 0
            };
        } catch (error) {
            console.error('Failed to fetch radio stations:', error);
            return { stations: [], total: 0 };
        }
    }

    // Get new releases
    async getNewReleases(limit = 20) {
        try {
            const data = await this.makeRequest(`chart/0/albums?limit=${limit}`, `new_releases_${limit}`);
            return {
                albums: data.data?.map(album => this.formatAlbum(album)) || [],
                total: data.total || 0
            };
        } catch (error) {
            console.error('Failed to fetch new releases:', error);
            return { albums: [], total: 0 };
        }
    }

    // Get artist recommendations based on another artist
    async getArtistRecommendations(artistId, limit = 10) {
        try {
            const data = await this.makeRequest(`artist/${artistId}/related?limit=${limit}`, `artist_related_${artistId}_${limit}`);
            return {
                artists: data.data?.map(artist => this.formatArtist(artist)) || [],
                total: data.total || 0
            };
        } catch (error) {
            console.error(`Failed to fetch artist recommendations for ${artistId}:`, error);
            return { artists: [], total: 0 };
        }
    }

    // Get track recommendations based on a track
    async getTrackRecommendations(trackId, limit = 10) {
        try {
            // Deezer doesn't have direct track recommendations, so we'll use the artist's top tracks
            const track = await this.makeRequest(`track/${trackId}`, `track_${trackId}`);
            if (track && track.artist) {
                const artistTracks = await this.makeRequest(`artist/${track.artist.id}/top?limit=${limit}`, `artist_top_${track.artist.id}_${limit}`);
                return {
                    tracks: artistTracks.data?.map(t => this.formatTrack(t)).filter(t => t.id !== trackId) || [],
                    total: artistTracks.total || 0
                };
            }
            return { tracks: [], total: 0 };
        } catch (error) {
            console.error(`Failed to fetch track recommendations for ${trackId}:`, error);
            return { tracks: [], total: 0 };
        }
    }

    // Get genre data
    async getGenres() {
        try {
            const data = await this.makeRequest('genre', 'genres');
            return data.data || [];
        } catch (error) {
            console.error('Failed to fetch genres:', error);
            return [];
        }
    }

    // Get tracks by genre
    async getGenreTracks(genreId, limit = 25) {
        try {
            const data = await this.makeRequest(`genre/${genreId}/artists`, `genre_${genreId}_${limit}`);
            return this.formatSearchData(data, 'track');
        } catch (error) {
            console.error(`Failed to fetch tracks for genre ${genreId}:`, error);
            return { data: [], total: 0 };
        }
    }

    // Format charts data for UI consistency
    formatChartsData(data) {
        const featured = data.tracks?.data?.[0];
        
        return {
            featured: featured ? {
                id: featured.id,
                title: featured.title,
                artist: featured.artist.name,
                image: featured.album.cover_xl || featured.album.cover_big || featured.album.cover,
                preview: featured.preview,
                duration: featured.duration,
                rank: featured.rank
            } : null,
            tracks: data.tracks?.data?.map(track => this.formatTrack(track)) || [],
            artists: data.artists?.data?.map(artist => this.formatArtist(artist)) || [],
            albums: data.albums?.data?.map(album => this.formatAlbum(album)) || [],
            playlists: data.playlists?.data?.map(playlist => this.formatPlaylist(playlist)) || []
        };
    }

    // Format search data
    formatSearchData(data, type) {
        if (!data || !data.data) {
            return { data: [], total: 0 };
        }

        let formattedData;
        switch (type) {
            case 'track':
                formattedData = data.data.map(track => this.formatTrack(track));
                break;
            case 'artist':
                formattedData = data.data.map(artist => this.formatArtist(artist));
                break;
            case 'album':
                formattedData = data.data.map(album => this.formatAlbum(album));
                break;
            case 'playlist':
                formattedData = data.data.map(playlist => this.formatPlaylist(playlist));
                break;
            default:
                formattedData = data.data.map(item => {
                    if (item.type === 'track') return this.formatTrack(item);
                    if (item.type === 'artist') return this.formatArtist(item);
                    if (item.type === 'album') return this.formatAlbum(item);
                    if (item.type === 'playlist') return this.formatPlaylist(item);
                    return item;
                });
        }

        return {
            data: formattedData,
            total: data.total || formattedData.length
        };
    }

    // Format individual track
    formatTrack(track) {
        return {
            id: track.id,
            title: track.title,
            artist: track.artist?.name || 'Unknown Artist',
            artistId: track.artist?.id,
            album: track.album?.title || 'Unknown Album',
            albumId: track.album?.id,
            cover: track.album?.cover_medium || track.album?.cover || 'imgs/album-01.png',
            coverLarge: track.album?.cover_xl || track.album?.cover_big || track.album?.cover,
            preview: track.preview,
            duration: track.duration,
            rank: track.rank,
            explicit: track.explicit_lyrics,
            type: 'track'
        };
    }

    // Format individual artist
    formatArtist(artist) {
        return {
            id: artist.id,
            name: artist.name,
            picture: artist.picture_medium || artist.picture || 'imgs/artist.png',
            pictureLarge: artist.picture_xl || artist.picture_big || artist.picture,
            fans: artist.nb_fan,
            albums: artist.nb_album,
            type: 'artist'
        };
    }

    // Format individual album
    formatAlbum(album) {
        return {
            id: album.id,
            title: album.title,
            artist: album.artist?.name || 'Unknown Artist',
            artistId: album.artist?.id,
            cover: album.cover_medium || album.cover || 'imgs/album-01.png',
            coverLarge: album.cover_xl || album.cover_big || album.cover,
            releaseDate: album.release_date,
            trackCount: album.nb_tracks,
            duration: album.duration,
            fans: album.fans,
            type: 'album'
        };
    }

    // Format individual playlist
    formatPlaylist(playlist) {
        return {
            id: playlist.id,
            title: playlist.title,
            description: playlist.description,
            cover: playlist.picture_medium || playlist.picture || 'imgs/playlist-01.png',
            coverLarge: playlist.picture_xl || playlist.picture_big || playlist.picture,
            trackCount: playlist.nb_tracks,
            duration: playlist.duration,
            fans: playlist.fans,
            creator: playlist.creator?.name || playlist.user?.name,
            type: 'playlist'
        };
    }

    // Format artist detailed data
    formatArtistData(artist, topTracks, albums) {
        return {
            ...this.formatArtist(artist),
            topTracks: topTracks.data?.map(track => this.formatTrack(track)) || [],
            albums: albums.data?.map(album => this.formatAlbum(album)) || []
        };
    }

    // Format album detailed data
    formatAlbumData(album) {
        return {
            ...this.formatAlbum(album),
            tracks: album.tracks?.data?.map(track => this.formatTrack(track)) || []
        };
    }

    // Format playlist detailed data
    formatPlaylistData(playlist) {
        return {
            ...this.formatPlaylist(playlist),
            tracks: playlist.tracks?.data?.map(track => this.formatTrack(track)) || []
        };
    }

    // Format radio station data
    formatRadioStation(station) {
        return {
            id: station.id,
            title: station.title,
            description: station.description || '',
            picture: station.picture_medium || station.picture || 'imgs/album-01.png',
            tracklist: station.tracklist || ''
        };
    }

    // Enhanced search with multiple content types
    async searchAll(query, limit = 25) {
        if (!query || query.trim().length < 2) {
            return { tracks: [], artists: [], albums: [], playlists: [], total: 0 };
        }

        try {
            const [tracks, artists, albums, playlists] = await Promise.all([
                this.search(query, 'track', limit),
                this.search(query, 'artist', Math.floor(limit / 2)),
                this.search(query, 'album', Math.floor(limit / 2)),
                this.search(query, 'playlist', Math.floor(limit / 3))
            ]);

            return {
                tracks: tracks.data || [],
                artists: artists.data || [],
                albums: albums.data || [],
                playlists: playlists.data || [],
                total: (tracks.total || 0) + (artists.total || 0) + (albums.total || 0) + (playlists.total || 0),
                query
            };
        } catch (error) {
            console.error('Failed to perform comprehensive search:', error);
            return { tracks: [], artists: [], albums: [], playlists: [], total: 0 };
        }
    }

    // Get trending content by genre
    async getTrendingByGenre(genreId, limit = 20) {
        try {
            const data = await this.makeRequest(`genre/${genreId}/artists?limit=${limit}`, `trending_genre_${genreId}_${limit}`);
            return {
                genre: genreId,
                content: data.data?.map(artist => this.formatArtist(artist)) || [],
                total: data.total || 0
            };
        } catch (error) {
            console.error(`Failed to fetch trending content for genre ${genreId}:`, error);
            return { genre: genreId, content: [], total: 0 };
        }
    }

    // Clear cache
    clearCache() {
        this.cache.clear();
        console.log('🎵 Deezer cache cleared');
    }

    // Get cache stats
    getCacheStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        };
    }

    // Performance optimization: Preload popular content
    async preloadPopularContent() {
        try {
            console.log('🎵 Preloading popular content...');

            // Preload charts and popular searches in background
            const preloadPromises = [
                this.getCharts(),
                this.search('pop', 'track', 10),
                this.search('rock', 'track', 10),
                this.getNewReleases(10)
            ];

            await Promise.allSettled(preloadPromises);
            console.log('✅ Popular content preloaded');
        } catch (error) {
            console.warn('⚠️ Failed to preload content:', error);
        }
    }
}

// Create global instance
window.deezerService = new DeezerService();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DeezerService;
}
