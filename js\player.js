// Enhanced Music Player with Backend Integration
class MusicPlayer {
    constructor() {
        this.apiBase = 'http://localhost:3001/api';
        this.deezerService = window.deezerService;
        this.bansheeUtils = window.bansheeUtils;

        // Player state
        this.currentTrack = null;
        this.playlist = [];
        this.currentIndex = 0;
        this.isPlaying = false;
        this.isShuffled = false;
        this.repeatMode = 'none'; // none, one, all
        this.volume = 1;

        // Audio context for visualizer
        this.audioContext = null;
        this.analyser = null;
        this.dataArray = null;

        this.init();
    }

    async init() {
        this.bindElements();
        this.bindEvents();
        this.setupAudioVisualizer();
        await this.loadPlayerData();
        this.setupKeyboardControls();
    }

    bindElements() {
        // Audio element
        this.audio = document.getElementById('audio');

        // Track info elements
        this.albumArt = document.querySelector('.album-art');
        this.songTitle = document.getElementById('song-title');
        this.artistName = document.getElementById('artist-name');
        this.playlistName = document.getElementById('playlist-name');

        // Control elements
        this.playPauseBtn = document.getElementById('play-pause');
        this.prevBtn = document.getElementById('prev');
        this.nextBtn = document.getElementById('next');
        this.shuffleBtn = document.getElementById('shuffle-btn');
        this.repeatBtn = document.getElementById('repeat-btn');
        this.volumeSlider = document.getElementById('volume');

        // Progress elements
        this.progressBar = document.getElementById('progress');
        this.currentTimeEl = document.getElementById('current-time');
        this.totalTimeEl = document.getElementById('total-time');

        // Additional controls
        this.hdBtn = document.getElementById('hd-btn');
        this.lyricsBtn = document.getElementById('lyrics-btn');
        this.queueBtn = document.getElementById('queue-btn');

        // Visualizer
        this.visualizerCanvas = document.getElementById('audioVisualizer');
        this.visualizerCtx = this.visualizerCanvas?.getContext('2d');
    }

    bindEvents() {
        // Audio events
        this.audio.addEventListener('loadedmetadata', () => this.updateDuration());
        this.audio.addEventListener('timeupdate', () => this.updateProgress());
        this.audio.addEventListener('ended', () => this.handleTrackEnd());
        this.audio.addEventListener('error', (e) => this.handleAudioError(e));

        // Control events
        this.playPauseBtn.addEventListener('click', () => this.togglePlayPause());
        this.prevBtn.addEventListener('click', () => this.previousTrack());
        this.nextBtn.addEventListener('click', () => this.nextTrack());
        this.shuffleBtn.addEventListener('click', () => this.toggleShuffle());
        this.repeatBtn.addEventListener('click', () => this.toggleRepeat());
        this.volumeSlider.addEventListener('input', (e) => this.setVolume(e.target.value));

        // Progress bar click
        this.progressBar.parentElement.addEventListener('click', (e) => this.seekTo(e));

        // Additional controls
        this.hdBtn?.addEventListener('click', () => this.toggleHD());
        this.lyricsBtn?.addEventListener('click', () => this.showLyrics());
        this.queueBtn?.addEventListener('click', () => this.showQueue());
    }

    // Backend connectivity methods
    async loadPlayerData() {
        try {
            console.log('🎵 Loading player data from backend...');

            // Load current playlist and track info
            const endpoints = [
                { url: '/player/current', fallback: this.getFallbackTrack() },
                { url: '/player/playlist', fallback: this.getFallbackPlaylist() },
                { url: '/player/queue', fallback: [] }
            ];

            const results = await this.bansheeUtils.fetchMultiple(endpoints);

            // Process current track
            if (results[0].status === 'fulfilled') {
                this.currentTrack = results[0].value;
                this.loadTrack(this.currentTrack);
            }

            // Process playlist
            if (results[1].status === 'fulfilled') {
                this.playlist = results[1].value.tracks || [];
                this.playlistName.textContent = results[1].value.name || 'Current Playlist';
            }

            console.log('✅ Player data loaded successfully!');
        } catch (error) {
            console.error('❌ Error loading player data:', error);
            this.loadFallbackData();
        }
    }

    getFallbackTrack() {
        return {
            id: 101,
            title: 'Blinding Lights',
            artist: 'The Weeknd',
            album: 'After Hours',
            cover: 'imgs/album-01.png',
            duration: 200,
            url: 'demo/demo-track.mp3'
        };
    }

    getFallbackPlaylist() {
        return {
            name: 'Demo Playlist',
            tracks: [this.getFallbackTrack()]
        };
    }

    loadFallbackData() {
        this.currentTrack = this.getFallbackTrack();
        this.playlist = [this.currentTrack];
        this.loadTrack(this.currentTrack);
        this.playlistName.textContent = 'Demo Playlist';
    }

    // Core player functionality
    loadTrack(track) {
        if (!track) return;

        this.currentTrack = track;
        this.audio.src = track.url || 'demo/demo-track.mp3';
        this.albumArt.src = track.cover || 'imgs/album-01.png';
        this.albumArt.alt = `${track.title} by ${track.artist}`;
        this.songTitle.textContent = track.title || 'Unknown Track';
        this.artistName.textContent = track.artist || 'Unknown Artist';

        // Update document title
        document.title = `${track.title} - ${track.artist} | BansheeBlast`;
    }

    togglePlayPause() {
        if (this.isPlaying) {
            this.pause();
        } else {
            this.play();
        }
    }

    async play() {
        try {
            await this.audio.play();
            this.isPlaying = true;
            this.updatePlayPauseButton();
            this.startVisualizer();
        } catch (error) {
            console.error('Error playing audio:', error);
        }
    }

    pause() {
        this.audio.pause();
        this.isPlaying = false;
        this.updatePlayPauseButton();
    }

    updatePlayPauseButton() {
        const playIcon = this.playPauseBtn.querySelector('.play-icon');
        const pauseIcon = this.playPauseBtn.querySelector('.pause-icon');

        if (this.isPlaying) {
            playIcon.classList.add('hidden');
            pauseIcon.classList.remove('hidden');
            this.playPauseBtn.setAttribute('aria-label', 'Pause track');
        } else {
            playIcon.classList.remove('hidden');
            pauseIcon.classList.add('hidden');
            this.playPauseBtn.setAttribute('aria-label', 'Play track');
        }
    }

    nextTrack() {
        if (this.playlist.length === 0) return;

        if (this.isShuffled) {
            this.currentIndex = Math.floor(Math.random() * this.playlist.length);
        } else {
            this.currentIndex = (this.currentIndex + 1) % this.playlist.length;
        }

        this.loadTrack(this.playlist[this.currentIndex]);
        if (this.isPlaying) this.play();
    }

    previousTrack() {
        if (this.playlist.length === 0) return;

        if (this.isShuffled) {
            this.currentIndex = Math.floor(Math.random() * this.playlist.length);
        } else {
            this.currentIndex = this.currentIndex === 0 ? this.playlist.length - 1 : this.currentIndex - 1;
        }

        this.loadTrack(this.playlist[this.currentIndex]);
        if (this.isPlaying) this.play();
    }

    handleTrackEnd() {
        if (this.repeatMode === 'one') {
            this.audio.currentTime = 0;
            this.play();
        } else if (this.repeatMode === 'all' || this.currentIndex < this.playlist.length - 1) {
            this.nextTrack();
        } else {
            this.pause();
        }
    }

    toggleShuffle() {
        this.isShuffled = !this.isShuffled;
        this.shuffleBtn.classList.toggle('active', this.isShuffled);
        this.shuffleBtn.setAttribute('aria-pressed', this.isShuffled);
    }

    toggleRepeat() {
        const modes = ['none', 'all', 'one'];
        const currentIndex = modes.indexOf(this.repeatMode);
        this.repeatMode = modes[(currentIndex + 1) % modes.length];

        this.repeatBtn.classList.toggle('active', this.repeatMode !== 'none');
        this.repeatBtn.setAttribute('aria-pressed', this.repeatMode !== 'none');

        // Update icon based on repeat mode
        const icon = this.repeatBtn.querySelector('i');
        if (this.repeatMode === 'one') {
            icon.className = 'fas fa-redo-alt';
        } else {
            icon.className = 'fas fa-redo';
        }
    }

    setVolume(value) {
        this.volume = parseFloat(value);
        this.audio.volume = this.volume;
    }

    seekTo(event) {
        const progressContainer = event.currentTarget;
        const rect = progressContainer.getBoundingClientRect();
        const percent = (event.clientX - rect.left) / rect.width;
        this.audio.currentTime = percent * this.audio.duration;
    }

    updateProgress() {
        if (this.audio.duration) {
            const percent = (this.audio.currentTime / this.audio.duration) * 100;
            this.progressBar.style.width = `${percent}%`;

            this.currentTimeEl.textContent = this.formatTime(this.audio.currentTime);
        }
    }

    updateDuration() {
        this.totalTimeEl.textContent = this.formatTime(this.audio.duration);
    }

    formatTime(seconds) {
        if (isNaN(seconds)) return '0:00';
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }

    handleAudioError(error) {
        console.error('Audio error:', error);
        this.pause();
        // Try to load from Deezer as fallback
        this.loadDeezerTrack();
    }

    async loadDeezerTrack() {
        if (this.deezerService && this.currentTrack) {
            try {
                const searchResults = await this.deezerService.search(
                    `${this.currentTrack.artist} ${this.currentTrack.title}`,
                    'track',
                    1
                );

                if (searchResults.data && searchResults.data.length > 0) {
                    const deezerTrack = searchResults.data[0];
                    // Note: Deezer preview URLs are 30-second clips
                    this.audio.src = deezerTrack.preview || this.audio.src;
                }
            } catch (error) {
                console.error('Failed to load Deezer track:', error);
            }
        }
    }

    // Audio visualizer setup
    setupAudioVisualizer() {
        if (!this.visualizerCanvas) return;

        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.analyser = this.audioContext.createAnalyser();
            const source = this.audioContext.createMediaElementSource(this.audio);

            source.connect(this.analyser);
            this.analyser.connect(this.audioContext.destination);

            this.analyser.fftSize = 256;
            this.dataArray = new Uint8Array(this.analyser.frequencyBinCount);

            this.resizeCanvas();
            window.addEventListener('resize', () => this.resizeCanvas());
        } catch (error) {
            console.warn('Audio visualizer not supported:', error);
        }
    }

    resizeCanvas() {
        if (!this.visualizerCanvas) return;

        const container = this.visualizerCanvas.parentElement;
        this.visualizerCanvas.width = container.clientWidth;
        this.visualizerCanvas.height = container.clientHeight;
    }

    startVisualizer() {
        if (!this.analyser || !this.visualizerCtx) return;

        const draw = () => {
            if (!this.isPlaying) return;

            requestAnimationFrame(draw);

            this.analyser.getByteFrequencyData(this.dataArray);

            this.visualizerCtx.fillStyle = 'rgba(0, 0, 0, 0.1)';
            this.visualizerCtx.fillRect(0, 0, this.visualizerCanvas.width, this.visualizerCanvas.height);

            const barWidth = (this.visualizerCanvas.width / this.dataArray.length) * 2.5;
            let barHeight;
            let x = 0;

            for (let i = 0; i < this.dataArray.length; i++) {
                barHeight = (this.dataArray[i] / 255) * this.visualizerCanvas.height;

                const r = barHeight + 25 * (i / this.dataArray.length);
                const g = 250 * (i / this.dataArray.length);
                const b = 50;

                this.visualizerCtx.fillStyle = `rgb(${r},${g},${b})`;
                this.visualizerCtx.fillRect(x, this.visualizerCanvas.height - barHeight, barWidth, barHeight);

                x += barWidth + 1;
            }
        };

        draw();
    }

    // Keyboard controls
    setupKeyboardControls() {
        document.addEventListener('keydown', (e) => {
            // Only handle if not typing in an input
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;

            switch (e.code) {
                case 'Space':
                    e.preventDefault();
                    this.togglePlayPause();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.previousTrack();
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.nextTrack();
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    this.setVolume(Math.min(1, this.volume + 0.1));
                    this.volumeSlider.value = this.volume;
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    this.setVolume(Math.max(0, this.volume - 0.1));
                    this.volumeSlider.value = this.volume;
                    break;
            }
        });
    }

    // Additional controls
    toggleHD() {
        this.hdBtn.classList.toggle('active');
        // HD functionality would be implemented here
        console.log('HD mode toggled');
    }

    showLyrics() {
        // Lyrics functionality would be implemented here
        console.log('Show lyrics');
    }

    showQueue() {
        // Queue functionality would be implemented here
        console.log('Show queue');
    }
}

// Initialize the music player when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.musicPlayer = new MusicPlayer();
});